# FreeRADIUS-GoogleSSO-Installer.ps1
# One-Click Installer for FreeRADIUS Google SSO Module
# Version: 1.0.0

param(
    [switch]$Silent = $false,
    [switch]$GUI = $true,
    [switch]$Help
)

# Set execution policy for this session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Import required modules
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Global variables
$script:InstallConfig = @{
    GoogleDomain = ""
    AdminEmail = ""
    ServiceAccountKeyPath = ""
    RadiusSecret = "testing123"
    InstallPath = "C:\FreeRADIUS-GoogleSSO"
    UseWSL2 = $true
    AutoStart = $true
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage
    
    # Ensure log directory exists
    $logDir = Split-Path $script:InstallConfig.InstallPath -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Force -Path $logDir | Out-Null
    }
    
    Add-Content -Path "$($script:InstallConfig.InstallPath)\install.log" -Value $logMessage -ErrorAction SilentlyContinue
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Show-WelcomeDialog {
    $form = New-Object System.Windows.Forms.Form
    $form.Text = "FreeRADIUS Google SSO Installer"
    $form.Size = New-Object System.Drawing.Size(600, 500)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.MinimizeBox = $false
    
    # Welcome label
    $welcomeLabel = New-Object System.Windows.Forms.Label
    $welcomeLabel.Location = New-Object System.Drawing.Point(20, 20)
    $welcomeLabel.Size = New-Object System.Drawing.Size(560, 60)
    $welcomeLabel.Text = "Welcome to FreeRADIUS Google SSO Module Installer`n`nThis installer will set up FreeRADIUS with Google Workspace authentication for WiFi access."
    $welcomeLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 10)
    $form.Controls.Add($welcomeLabel)
    
    # Configuration group
    $configGroup = New-Object System.Windows.Forms.GroupBox
    $configGroup.Location = New-Object System.Drawing.Point(20, 90)
    $configGroup.Size = New-Object System.Drawing.Size(560, 280)
    $configGroup.Text = "Configuration"
    $form.Controls.Add($configGroup)
    
    # Google Domain
    $domainLabel = New-Object System.Windows.Forms.Label
    $domainLabel.Location = New-Object System.Drawing.Point(20, 30)
    $domainLabel.Size = New-Object System.Drawing.Size(150, 20)
    $domainLabel.Text = "Google Workspace Domain:"
    $configGroup.Controls.Add($domainLabel)
    
    $domainTextBox = New-Object System.Windows.Forms.TextBox
    $domainTextBox.Location = New-Object System.Drawing.Point(180, 28)
    $domainTextBox.Size = New-Object System.Drawing.Size(350, 20)
    $domainTextBox.Text = $script:InstallConfig.GoogleDomain
    $configGroup.Controls.Add($domainTextBox)
    
    # Admin Email
    $emailLabel = New-Object System.Windows.Forms.Label
    $emailLabel.Location = New-Object System.Drawing.Point(20, 60)
    $emailLabel.Size = New-Object System.Drawing.Size(150, 20)
    $emailLabel.Text = "Admin Email:"
    $configGroup.Controls.Add($emailLabel)
    
    $emailTextBox = New-Object System.Windows.Forms.TextBox
    $emailTextBox.Location = New-Object System.Drawing.Point(180, 58)
    $emailTextBox.Size = New-Object System.Drawing.Size(350, 20)
    $emailTextBox.Text = $script:InstallConfig.AdminEmail
    $configGroup.Controls.Add($emailTextBox)
    
    # Service Account Key
    $keyLabel = New-Object System.Windows.Forms.Label
    $keyLabel.Location = New-Object System.Drawing.Point(20, 90)
    $keyLabel.Size = New-Object System.Drawing.Size(150, 20)
    $keyLabel.Text = "Service Account Key:"
    $configGroup.Controls.Add($keyLabel)
    
    $keyTextBox = New-Object System.Windows.Forms.TextBox
    $keyTextBox.Location = New-Object System.Drawing.Point(180, 88)
    $keyTextBox.Size = New-Object System.Drawing.Size(280, 20)
    $keyTextBox.Text = $script:InstallConfig.ServiceAccountKeyPath
    $configGroup.Controls.Add($keyTextBox)
    
    $browseButton = New-Object System.Windows.Forms.Button
    $browseButton.Location = New-Object System.Drawing.Point(470, 86)
    $browseButton.Size = New-Object System.Drawing.Size(60, 25)
    $browseButton.Text = "Browse"
    $browseButton.Add_Click({
        $openFileDialog = New-Object System.Windows.Forms.OpenFileDialog
        $openFileDialog.Filter = "JSON files (*.json)|*.json|All files (*.*)|*.*"
        $openFileDialog.Title = "Select Google Service Account Key File"
        if ($openFileDialog.ShowDialog() -eq "OK") {
            $keyTextBox.Text = $openFileDialog.FileName
        }
    })
    $configGroup.Controls.Add($browseButton)
    
    # RADIUS Secret
    $secretLabel = New-Object System.Windows.Forms.Label
    $secretLabel.Location = New-Object System.Drawing.Point(20, 120)
    $secretLabel.Size = New-Object System.Drawing.Size(150, 20)
    $secretLabel.Text = "RADIUS Shared Secret:"
    $configGroup.Controls.Add($secretLabel)
    
    $secretTextBox = New-Object System.Windows.Forms.TextBox
    $secretTextBox.Location = New-Object System.Drawing.Point(180, 118)
    $secretTextBox.Size = New-Object System.Drawing.Size(350, 20)
    $secretTextBox.Text = $script:InstallConfig.RadiusSecret
    $configGroup.Controls.Add($secretTextBox)
    
    # Installation options
    $optionsGroup = New-Object System.Windows.Forms.GroupBox
    $optionsGroup.Location = New-Object System.Drawing.Point(20, 150)
    $optionsGroup.Size = New-Object System.Drawing.Size(520, 100)
    $optionsGroup.Text = "Installation Options"
    $configGroup.Controls.Add($optionsGroup)
    
    $wsl2CheckBox = New-Object System.Windows.Forms.CheckBox
    $wsl2CheckBox.Location = New-Object System.Drawing.Point(20, 25)
    $wsl2CheckBox.Size = New-Object System.Drawing.Size(200, 20)
    $wsl2CheckBox.Text = "Use WSL2 (Recommended)"
    $wsl2CheckBox.Checked = $script:InstallConfig.UseWSL2
    $optionsGroup.Controls.Add($wsl2CheckBox)
    
    $autostartCheckBox = New-Object System.Windows.Forms.CheckBox
    $autostartCheckBox.Location = New-Object System.Drawing.Point(20, 50)
    $autostartCheckBox.Size = New-Object System.Drawing.Size(200, 20)
    $autostartCheckBox.Text = "Start service automatically"
    $autostartCheckBox.Checked = $script:InstallConfig.AutoStart
    $optionsGroup.Controls.Add($autostartCheckBox)
    
    # Buttons
    $installButton = New-Object System.Windows.Forms.Button
    $installButton.Location = New-Object System.Drawing.Point(400, 390)
    $installButton.Size = New-Object System.Drawing.Size(80, 30)
    $installButton.Text = "Install"
    $installButton.DialogResult = "OK"
    $installButton.Add_Click({
        # Validate inputs
        if ([string]::IsNullOrWhiteSpace($domainTextBox.Text)) {
            [System.Windows.Forms.MessageBox]::Show("Please enter your Google Workspace domain.", "Validation Error", "OK", "Warning")
            return
        }
        if ([string]::IsNullOrWhiteSpace($emailTextBox.Text)) {
            [System.Windows.Forms.MessageBox]::Show("Please enter your admin email.", "Validation Error", "OK", "Warning")
            return
        }
        if ([string]::IsNullOrWhiteSpace($keyTextBox.Text) -or -not (Test-Path $keyTextBox.Text)) {
            [System.Windows.Forms.MessageBox]::Show("Please select a valid service account key file.", "Validation Error", "OK", "Warning")
            return
        }
        
        # Save configuration
        $script:InstallConfig.GoogleDomain = $domainTextBox.Text
        $script:InstallConfig.AdminEmail = $emailTextBox.Text
        $script:InstallConfig.ServiceAccountKeyPath = $keyTextBox.Text
        $script:InstallConfig.RadiusSecret = $secretTextBox.Text
        $script:InstallConfig.UseWSL2 = $wsl2CheckBox.Checked
        $script:InstallConfig.AutoStart = $autostartCheckBox.Checked
        
        $form.DialogResult = "OK"
        $form.Close()
    })
    $form.Controls.Add($installButton)
    
    $cancelButton = New-Object System.Windows.Forms.Button
    $cancelButton.Location = New-Object System.Drawing.Point(500, 390)
    $cancelButton.Size = New-Object System.Drawing.Size(80, 30)
    $cancelButton.Text = "Cancel"
    $cancelButton.DialogResult = "Cancel"
    $form.Controls.Add($cancelButton)
    
    $form.AcceptButton = $installButton
    $form.CancelButton = $cancelButton
    
    return $form.ShowDialog()
}

function Test-WSL2Status {
    Write-Log "Testing WSL2 status..."

    # Check if WSL command is available
    try {
        $wslVersion = wsl --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Log "WSL command not available" "WARNING"
            return @{ Available = $false; Reason = "WSL command not found" }
        }

        Write-Log "WSL version info: $wslVersion"

        # Check WSL status
        $wslStatus = wsl --status 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Log "WSL status check failed" "WARNING"
            return @{ Available = $false; Reason = "WSL status check failed" }
        }

        Write-Log "WSL status: $wslStatus"

        # Check for Ubuntu distributions
        $distributions = wsl --list --quiet 2>$null
        if ($LASTEXITCODE -eq 0) {
            $hasUbuntu = ($distributions -contains "Ubuntu-20.04" -or $distributions -contains "Ubuntu")
            Write-Log "Available distributions: $distributions"
            Write-Log "Ubuntu installed: $hasUbuntu"

            return @{
                Available = $true
                HasUbuntu = $hasUbuntu
                Distributions = $distributions
            }
        } else {
            Write-Log "Could not list WSL distributions" "WARNING"
            return @{ Available = $true; HasUbuntu = $false; Distributions = @() }
        }

    } catch {
        Write-Log "Error testing WSL2 status: $($_.Exception.Message)" "ERROR"
        return @{ Available = $false; Reason = $_.Exception.Message }
    }
}

function Install-Prerequisites {
    Write-Log "Installing prerequisites..."
    
    # Create progress form
    $progressForm = New-Object System.Windows.Forms.Form
    $progressForm.Text = "Installing Prerequisites"
    $progressForm.Size = New-Object System.Drawing.Size(500, 200)
    $progressForm.StartPosition = "CenterScreen"
    $progressForm.FormBorderStyle = "FixedDialog"
    $progressForm.MaximizeBox = $false
    $progressForm.MinimizeBox = $false
    
    $progressLabel = New-Object System.Windows.Forms.Label
    $progressLabel.Location = New-Object System.Drawing.Point(20, 20)
    $progressLabel.Size = New-Object System.Drawing.Size(460, 40)
    $progressLabel.Text = "Installing prerequisites, please wait..."
    $progressForm.Controls.Add($progressLabel)
    
    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 70)
    $progressBar.Size = New-Object System.Drawing.Size(460, 30)
    $progressBar.Style = "Marquee"
    $progressForm.Controls.Add($progressBar)
    
    $logTextBox = New-Object System.Windows.Forms.TextBox
    $logTextBox.Location = New-Object System.Drawing.Point(20, 110)
    $logTextBox.Size = New-Object System.Drawing.Size(460, 50)
    $logTextBox.Multiline = $true
    $logTextBox.ScrollBars = "Vertical"
    $logTextBox.ReadOnly = $true
    $progressForm.Controls.Add($logTextBox)
    
    $progressForm.Show()
    $progressForm.Refresh()
    
    try {
        # Install Chocolatey
        $progressLabel.Text = "Installing Chocolatey package manager..."
        $progressForm.Refresh()
        
        if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
            Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
            $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        }
        
        # Install required tools
        $progressLabel.Text = "Installing required tools (Git, 7-Zip, NSSM)..."
        $progressForm.Refresh()
        
        $chocoOutput = choco install git 7zip nssm -y 2>&1
        $logTextBox.Text += $chocoOutput + "`r`n"
        $logTextBox.Refresh()
        
        if ($script:InstallConfig.UseWSL2) {
            # Enable WSL2
            $progressLabel.Text = "Enabling WSL2 features..."
            $progressForm.Refresh()

            Write-Log "Enabling WSL2 features..."
            $wslResult = dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
            $vmResult = dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

            # Log DISM results
            Write-Log "WSL feature enable result: $wslResult"
            Write-Log "VM Platform feature enable result: $vmResult"

            # Check if WSL is available
            $progressLabel.Text = "Verifying WSL2 availability..."
            $progressForm.Refresh()

            $wslAvailable = $false
            try {
                $wslVersion = wsl --version 2>$null
                if ($LASTEXITCODE -eq 0) {
                    $wslAvailable = $true
                    Write-Log "WSL is available: $wslVersion"
                } else {
                    Write-Log "WSL command not available, may need restart" "WARNING"
                }
            } catch {
                Write-Log "WSL not available: $($_.Exception.Message)" "WARNING"
            }

            # Check if Ubuntu is already installed
            $progressLabel.Text = "Checking for existing Ubuntu installation..."
            $progressForm.Refresh()

            $ubuntuInstalled = $false
            if ($wslAvailable) {
                try {
                    $distributions = wsl --list --quiet 2>$null
                    Write-Log "WSL distributions found: $distributions"

                    if ($LASTEXITCODE -eq 0) {
                        # Check for various Ubuntu distribution names
                        $ubuntuFound = $false
                        foreach ($dist in $distributions) {
                            if ($dist -like "*Ubuntu*" -and $dist -notlike "*docker*") {
                                $ubuntuFound = $true
                                Write-Log "Found Ubuntu distribution: $dist"
                                break
                            }
                        }

                        if ($ubuntuFound) {
                            $ubuntuInstalled = $true
                            Write-Log "Ubuntu is already installed in WSL2"
                        } else {
                            Write-Log "No Ubuntu distribution found in WSL2"
                        }
                    } else {
                        Write-Log "Failed to list WSL distributions (exit code: $LASTEXITCODE)"
                    }
                } catch {
                    Write-Log "Could not check WSL distributions: $($_.Exception.Message)" "WARNING"
                }
            }

            if (-not $ubuntuInstalled) {
                $progressLabel.Text = "Installing Ubuntu in WSL2 (this may take several minutes)..."
                $progressForm.Refresh()

                Write-Log "Starting Ubuntu installation..."

                if ($wslAvailable) {
                    # Try to install Ubuntu
                    try {
                        $installResult = wsl --install -d Ubuntu-20.04 --no-launch 2>&1
                        Write-Log "Ubuntu install command result: $installResult"

                        # Check if Ubuntu is already installed based on the output
                        if ($installResult -like "*already installed*" -or $installResult -like "*operation completed successfully*") {
                            Write-Log "Ubuntu is already installed, skipping installation wait"
                            $installed = $true
                        } elseif ($LASTEXITCODE -ne 0) {
                            throw "WSL install command failed with exit code $LASTEXITCODE"
                        } else {
                            # Installation is in progress, wait for completion
                            $installed = $false
                        }

                        # Only wait if installation is actually in progress
                        if (-not $installed) {
                            # Wait for installation to complete with timeout
                            $progressLabel.Text = "Waiting for Ubuntu installation to complete..."
                            $progressForm.Refresh()

                            $timeout = 300 # 5 minutes timeout
                            $elapsed = 0

                            do {
                                Start-Sleep -Seconds 10
                                $elapsed += 10

                                # Update progress
                                $progressLabel.Text = "Installing Ubuntu... ($elapsed seconds elapsed)"
                                $progressForm.Refresh()

                                try {
                                    $distributions = wsl --list --quiet 2>$null
                                    if ($LASTEXITCODE -eq 0 -and ($distributions -contains "Ubuntu-20.04" -or $distributions -contains "Ubuntu")) {
                                        $installed = $true
                                        Write-Log "Ubuntu installation completed successfully"
                                        break
                                    }
                                } catch {
                                    Write-Log "Error checking installation status: $($_.Exception.Message)" "WARNING"
                                }

                                if ($elapsed -ge $timeout) {
                                    Write-Log "Ubuntu installation timeout after $timeout seconds" "ERROR"
                                    throw "Ubuntu installation timed out"
                                }

                            } while (-not $installed)
                        }

                        # Verify Ubuntu is actually working
                        if ($installed) {
                            $progressLabel.Text = "Verifying Ubuntu installation..."
                            $progressForm.Refresh()

                            try {
                                # Test if we can run a simple command in Ubuntu
                                $testResult = wsl -d Ubuntu-20.04 -e echo "test" 2>$null
                                if ($LASTEXITCODE -eq 0) {
                                    Write-Log "Ubuntu verification successful"
                                } else {
                                    Write-Log "Ubuntu verification failed, trying default Ubuntu..." "WARNING"
                                    $testResult = wsl -d Ubuntu -e echo "test" 2>$null
                                    if ($LASTEXITCODE -eq 0) {
                                        Write-Log "Default Ubuntu verification successful"
                                    } else {
                                        Write-Log "Ubuntu installation verification failed" "ERROR"
                                        throw "Ubuntu is installed but not responding to commands"
                                    }
                                }
                            } catch {
                                Write-Log "Error verifying Ubuntu installation: $($_.Exception.Message)" "WARNING"
                            }
                        }

                    } catch {
                        Write-Log "Error during Ubuntu installation: $($_.Exception.Message)" "ERROR"

                        # Check if restart is needed
                        $restartNeeded = $false
                        if ($_.Exception.Message -like "*restart*" -or $_.Exception.Message -like "*reboot*") {
                            $restartNeeded = $true
                        }

                        if ($restartNeeded) {
                            $progressForm.Close()
                            $restartChoice = [System.Windows.Forms.MessageBox]::Show(
                                "WSL2 installation requires a system restart to complete.`n`nWould you like to restart now and continue the installation after restart?`n`nClick Yes to restart now, or No to continue without WSL2.",
                                "Restart Required",
                                "YesNo",
                                "Question"
                            )

                            if ($restartChoice -eq "Yes") {
                                Write-Log "User chose to restart system for WSL2 completion"
                                Restart-Computer -Force
                                exit
                            } else {
                                Write-Log "User chose to continue without WSL2" "WARNING"
                                $script:InstallConfig.UseWSL2 = $false
                                return $true
                            }
                        } else {
                            throw $_
                        }
                    }
                } else {
                    Write-Log "WSL not available, may need system restart" "ERROR"
                    $progressForm.Close()

                    $restartChoice = [System.Windows.Forms.MessageBox]::Show(
                        "WSL2 features have been enabled but require a system restart to become available.`n`nWould you like to restart now and continue the installation after restart?",
                        "Restart Required",
                        "YesNo",
                        "Question"
                    )

                    if ($restartChoice -eq "Yes") {
                        Write-Log "User chose to restart system for WSL2 activation"
                        Restart-Computer -Force
                        exit
                    } else {
                        Write-Log "User chose to continue without WSL2" "WARNING"
                        $script:InstallConfig.UseWSL2 = $false
                        return $true
                    }
                }
            }
        }
        
        $progressForm.Close()
        Write-Log "Prerequisites installed successfully"
        return $true

    } catch {
        $progressForm.Close()
        Write-Log "Error installing prerequisites: $($_.Exception.Message)" "ERROR"

        # Provide more specific error guidance
        $errorMessage = $_.Exception.Message
        $guidance = ""

        if ($errorMessage -like "*WSL*" -or $errorMessage -like "*Ubuntu*") {
            $guidance = "`n`nWSL2 Installation Tips:`n" +
                       "• Ensure Windows 10 version 2004+ or Windows 11`n" +
                       "• Run installer as Administrator`n" +
                       "• Check Windows Features: WSL and Virtual Machine Platform`n" +
                       "• Restart may be required after enabling WSL2 features`n" +
                       "• Check internet connection for Ubuntu download`n" +
                       "• Run 'diagnose-wsl2.bat' for detailed troubleshooting"
        }

        [System.Windows.Forms.MessageBox]::Show(
            "Error installing prerequisites: $errorMessage$guidance",
            "Installation Error",
            "OK",
            "Error"
        )
        return $false
    }
}

function Deploy-FreeRADIUS {
    Write-Log "Deploying FreeRADIUS Google SSO module..."

    # Create progress form
    $progressForm = New-Object System.Windows.Forms.Form
    $progressForm.Text = "Installing FreeRADIUS Google SSO"
    $progressForm.Size = New-Object System.Drawing.Size(500, 300)
    $progressForm.StartPosition = "CenterScreen"
    $progressForm.FormBorderStyle = "FixedDialog"
    $progressForm.MaximizeBox = $false
    $progressForm.MinimizeBox = $false

    $progressLabel = New-Object System.Windows.Forms.Label
    $progressLabel.Location = New-Object System.Drawing.Point(20, 20)
    $progressLabel.Size = New-Object System.Drawing.Size(460, 40)
    $progressLabel.Text = "Deploying FreeRADIUS with Google SSO..."
    $progressForm.Controls.Add($progressLabel)

    $progressBar = New-Object System.Windows.Forms.ProgressBar
    $progressBar.Location = New-Object System.Drawing.Point(20, 70)
    $progressBar.Size = New-Object System.Drawing.Size(460, 30)
    $progressBar.Maximum = 100
    $progressForm.Controls.Add($progressBar)

    $logTextBox = New-Object System.Windows.Forms.TextBox
    $logTextBox.Location = New-Object System.Drawing.Point(20, 110)
    $logTextBox.Size = New-Object System.Drawing.Size(460, 150)
    $logTextBox.Multiline = $true
    $logTextBox.ScrollBars = "Vertical"
    $logTextBox.ReadOnly = $true
    $progressForm.Controls.Add($logTextBox)

    $progressForm.Show()
    $progressForm.Refresh()

    try {
        # Step 1: Setup WSL2 environment
        $progressLabel.Text = "Setting up WSL2 environment..."
        $progressBar.Value = 10
        $progressForm.Refresh()

        if ($script:InstallConfig.UseWSL2) {
            # Start Ubuntu if not running
            wsl -d Ubuntu-20.04 -e true 2>$null
            if ($LASTEXITCODE -ne 0) {
                # First time setup - this will prompt user for username/password
                [System.Windows.Forms.MessageBox]::Show("Ubuntu setup required. Please complete the Ubuntu user setup in the console window that will open.", "Ubuntu Setup", "OK", "Information")
                wsl -d Ubuntu-20.04
            }

            # Update package lists
            $progressLabel.Text = "Updating package lists in WSL2..."
            $progressBar.Value = 20
            $progressForm.Refresh()

            wsl -d Ubuntu-20.04 sudo apt update
        }

        # Step 2: Install dependencies
        $progressLabel.Text = "Installing FreeRADIUS dependencies..."
        $progressBar.Value = 30
        $progressForm.Refresh()

        $installCmd = "sudo apt install -y build-essential cmake pkg-config libcurl4-openssl-dev libjson-c-dev libssl-dev freeradius freeradius-utils"
        wsl -d Ubuntu-20.04 bash -c $installCmd

        # Step 3: Copy source files
        $progressLabel.Text = "Copying source files to WSL2..."
        $progressBar.Value = 40
        $progressForm.Refresh()

        $wslTempDir = "/tmp/freeradius-google-sso"
        wsl -d Ubuntu-20.04 rm -rf $wslTempDir
        wsl -d Ubuntu-20.04 mkdir -p $wslTempDir

        # Copy files from installer directory
        $sourceDir = $PSScriptRoot
        $windowsPath = (Resolve-Path $sourceDir).Path
        $wslPath = $windowsPath -replace '^([A-Z]):', '/mnt/$1' -replace '\\', '/' | ForEach-Object { $_.ToLower() }

        wsl -d Ubuntu-20.04 cp -r "$wslPath/src" "$wslPath/config" "$wslPath/CMakeLists.txt" "$wslPath/dictionary.google_sso" "$wslPath/scripts" $wslTempDir/

        # Step 4: Build module
        $progressLabel.Text = "Building FreeRADIUS module..."
        $progressBar.Value = 60
        $progressForm.Refresh()

        wsl -d Ubuntu-20.04 bash -c "cd $wslTempDir && mkdir -p build && cd build && cmake .. && make"

        # Step 5: Install module
        $progressLabel.Text = "Installing module..."
        $progressBar.Value = 80
        $progressForm.Refresh()

        wsl -d Ubuntu-20.04 bash -c "cd $wslTempDir && sudo ./scripts/setup.sh install"

        # Step 6: Configure module
        $progressLabel.Text = "Configuring module..."
        $progressBar.Value = 90
        $progressForm.Refresh()

        # Get service account email from JSON file
        $serviceAccountEmail = Get-ServiceAccountEmail -KeyPath $script:InstallConfig.ServiceAccountKeyPath

        # Create module configuration
        $moduleConfig = @"
google_sso {
    service_account_email = "$serviceAccountEmail"
    service_account_key = "/etc/freeradius/certs/google-service-account.json"
    domain = "$($script:InstallConfig.GoogleDomain)"
    admin_email = "$($script:InstallConfig.AdminEmail)"

    api_timeout = 30
    token_cache_ttl = 3600

    enable_certificate_auth = no
    enable_password_fallback = yes

    max_auth_attempts = 5
    lockout_duration = 900
    require_group_membership = no

    user_cache_ttl = 300
    group_cache_ttl = 600
    enable_offline_auth = yes

    debug_mode = yes
}
"@

        # Write configuration to WSL2
        $moduleConfig | wsl -d Ubuntu-20.04 sudo tee /etc/freeradius/mods-enabled/google_sso

        # Copy service account key
        $keyWindowsPath = (Resolve-Path $script:InstallConfig.ServiceAccountKeyPath).Path
        $keyWslPath = $keyWindowsPath -replace '^([A-Z]):', '/mnt/$1' -replace '\\', '/' | ForEach-Object { $_.ToLower() }
        wsl -d Ubuntu-20.04 sudo cp $keyWslPath /etc/freeradius/certs/google-service-account.json
        wsl -d Ubuntu-20.04 sudo chown freerad:freerad /etc/freeradius/certs/google-service-account.json
        wsl -d Ubuntu-20.04 sudo chmod 600 /etc/freeradius/certs/google-service-account.json

        $progressBar.Value = 100
        $progressForm.Close()

        Write-Log "FreeRADIUS deployment completed successfully"
        return $true

    } catch {
        $progressForm.Close()
        Write-Log "Error deploying FreeRADIUS: $($_.Exception.Message)" "ERROR"
        [System.Windows.Forms.MessageBox]::Show("Error deploying FreeRADIUS: $($_.Exception.Message)", "Deployment Error", "OK", "Error")
        return $false
    }
}

function Get-ServiceAccountEmail {
    param([string]$KeyPath)

    try {
        $keyContent = Get-Content $KeyPath | ConvertFrom-Json
        return $keyContent.client_email
    } catch {
        Write-Log "Could not extract service account email from JSON file" "WARNING"
        return "<EMAIL>"
    }
}

function Install-WindowsService {
    Write-Log "Installing Windows service..."

    try {
        # Create service directory
        $serviceDir = $script:InstallConfig.InstallPath
        New-Item -ItemType Directory -Force -Path $serviceDir | Out-Null

        # Create service wrapper script
        $serviceScript = @"
# FreeRADIUS WSL2 Service Wrapper
`$ErrorActionPreference = "Continue"

Write-EventLog -LogName Application -Source "FreeRADIUS-GoogleSSO" -EventId 1000 -Message "Starting FreeRADIUS in WSL2" -EntryType Information

# Start FreeRADIUS in WSL2
wsl -d Ubuntu-20.04 sudo systemctl start freeradius

# Monitor and restart if needed
while (`$true) {
    Start-Sleep -Seconds 30

    `$status = wsl -d Ubuntu-20.04 systemctl is-active freeradius
    if (`$status -ne "active") {
        Write-EventLog -LogName Application -Source "FreeRADIUS-GoogleSSO" -EventId 1001 -Message "FreeRADIUS stopped, restarting..." -EntryType Warning
        wsl -d Ubuntu-20.04 sudo systemctl start freeradius
    }
}
"@

        $serviceScript | Out-File -FilePath "$serviceDir\service-wrapper.ps1" -Encoding UTF8

        # Create event log source
        try {
            New-EventLog -LogName Application -Source "FreeRADIUS-GoogleSSO" -ErrorAction SilentlyContinue
        } catch {
            # Source might already exist
        }

        # Install service using NSSM
        $serviceName = "FreeRADIUS-GoogleSSO"

        # Remove existing service if it exists
        $existingService = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($existingService) {
            Stop-Service -Name $serviceName -Force -ErrorAction SilentlyContinue
            nssm remove $serviceName confirm
        }

        # Install new service
        nssm install $serviceName powershell.exe
        nssm set $serviceName Arguments "-ExecutionPolicy Bypass -WindowStyle Hidden -File `"$serviceDir\service-wrapper.ps1`""
        nssm set $serviceName DisplayName "FreeRADIUS with Google SSO"
        nssm set $serviceName Description "FreeRADIUS server with Google SSO authentication running in WSL2"
        nssm set $serviceName Start SERVICE_AUTO_START
        nssm set $serviceName AppStdout "$serviceDir\service-output.log"
        nssm set $serviceName AppStderr "$serviceDir\service-error.log"

        if ($script:InstallConfig.AutoStart) {
            Start-Service -Name $serviceName
        }

        # Copy management utility if it exists
        if (Test-Path "$PSScriptRoot\freeradius-manager.ps1") {
            Copy-Item -Path "$PSScriptRoot\freeradius-manager.ps1" -Destination "$serviceDir\" -Force
        }

        Write-Log "Windows service installed successfully"
        return $true

    } catch {
        Write-Log "Error installing Windows service: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Configure-WindowsFirewall {
    Write-Log "Configuring Windows Firewall..."

    try {
        # Remove existing rules if they exist
        Remove-NetFirewallRule -DisplayName "FreeRADIUS-Auth" -ErrorAction SilentlyContinue
        Remove-NetFirewallRule -DisplayName "FreeRADIUS-Acct" -ErrorAction SilentlyContinue

        # Add new rules
        New-NetFirewallRule -DisplayName "FreeRADIUS-Auth" -Direction Inbound -Protocol UDP -LocalPort 1812 -Action Allow
        New-NetFirewallRule -DisplayName "FreeRADIUS-Acct" -Direction Inbound -Protocol UDP -LocalPort 1813 -Action Allow

        Write-Log "Windows Firewall configured successfully"
        return $true

    } catch {
        Write-Log "Warning: Failed to configure Windows Firewall: $($_.Exception.Message)" "WARNING"
        return $false
    }
}

function Create-DesktopShortcuts {
    Write-Log "Creating desktop shortcuts..."

    try {
        $WshShell = New-Object -ComObject WScript.Shell
        $DesktopPath = [System.Environment]::GetFolderPath("Desktop")

        # Create FreeRADIUS Manager shortcut
        if (Test-Path "$($script:InstallConfig.InstallPath)\freeradius-manager.ps1") {
            $ManagerShortcut = $WshShell.CreateShortcut("$DesktopPath\FreeRADIUS Manager.lnk")
            $ManagerShortcut.TargetPath = "powershell.exe"
            $ManagerShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$($script:InstallConfig.InstallPath)\freeradius-manager.ps1`""
            $ManagerShortcut.WorkingDirectory = $script:InstallConfig.InstallPath
            $ManagerShortcut.Description = "FreeRADIUS Google SSO Manager"
            $ManagerShortcut.IconLocation = "shell32.dll,21"
            $ManagerShortcut.Save()
        }

        # Create Services shortcut
        $ServicesShortcut = $WshShell.CreateShortcut("$DesktopPath\Windows Services.lnk")
        $ServicesShortcut.TargetPath = "services.msc"
        $ServicesShortcut.Description = "Windows Services Console"
        $ServicesShortcut.IconLocation = "shell32.dll,14"
        $ServicesShortcut.Save()

        Write-Log "Desktop shortcuts created successfully"
        return $true

    } catch {
        Write-Log "Warning: Failed to create desktop shortcuts: $($_.Exception.Message)" "WARNING"
        return $false
    }
}

function Test-Installation {
    Write-Log "Testing installation..."

    try {
        # Test FreeRADIUS configuration
        $testResult = wsl -d Ubuntu-20.04 sudo freeradius -C
        if ($LASTEXITCODE -eq 0) {
            Write-Log "FreeRADIUS configuration test passed"

            # Test service status
            $serviceStatus = Get-Service -Name "FreeRADIUS-GoogleSSO" -ErrorAction SilentlyContinue
            if ($serviceStatus -and $serviceStatus.Status -eq "Running") {
                Write-Log "Windows service is running"
                return $true
            } else {
                Write-Log "Windows service is not running" "WARNING"
                return $false
            }
        } else {
            Write-Log "FreeRADIUS configuration test failed" "ERROR"
            return $false
        }

    } catch {
        Write-Log "Error testing installation: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Show-CompletionDialog {
    param([bool]$Success)

    $form = New-Object System.Windows.Forms.Form
    $form.Text = "Installation Complete"
    $form.Size = New-Object System.Drawing.Size(600, 500)
    $form.StartPosition = "CenterScreen"
    $form.FormBorderStyle = "FixedDialog"
    $form.MaximizeBox = $false
    $form.MinimizeBox = $false

    if ($Success) {
        $statusLabel = New-Object System.Windows.Forms.Label
        $statusLabel.Location = New-Object System.Drawing.Point(20, 20)
        $statusLabel.Size = New-Object System.Drawing.Size(560, 40)
        $statusLabel.Text = "FreeRADIUS Google SSO has been installed successfully!"
        $statusLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 12, [System.Drawing.FontStyle]::Bold)
        $statusLabel.ForeColor = [System.Drawing.Color]::Green
        $form.Controls.Add($statusLabel)

        # Configuration summary
        $summaryGroup = New-Object System.Windows.Forms.GroupBox
        $summaryGroup.Location = New-Object System.Drawing.Point(20, 70)
        $summaryGroup.Size = New-Object System.Drawing.Size(560, 200)
        $summaryGroup.Text = "Configuration Summary"
        $form.Controls.Add($summaryGroup)

        $summaryText = @"
Google Domain: $($script:InstallConfig.GoogleDomain)
Admin Email: $($script:InstallConfig.AdminEmail)
RADIUS Secret: $($script:InstallConfig.RadiusSecret)
Installation Path: $($script:InstallConfig.InstallPath)
Deployment Method: $(if ($script:InstallConfig.UseWSL2) { 'WSL2' } else { 'Native Windows' })

RADIUS Server Configuration:
- Authentication Port: 1812
- Accounting Port: 1813
- Server IP: $(try { (wsl -d Ubuntu-20.04 hostname -I).Trim() } catch { 'localhost' })

Service Status: $(try { (Get-Service -Name 'FreeRADIUS-GoogleSSO').Status } catch { 'Unknown' })
"@

        $summaryTextBox = New-Object System.Windows.Forms.TextBox
        $summaryTextBox.Location = New-Object System.Drawing.Point(20, 25)
        $summaryTextBox.Size = New-Object System.Drawing.Size(520, 160)
        $summaryTextBox.Multiline = $true
        $summaryTextBox.ScrollBars = "Vertical"
        $summaryTextBox.ReadOnly = $true
        $summaryTextBox.Text = $summaryText
        $summaryGroup.Controls.Add($summaryTextBox)

        # Next steps
        $stepsGroup = New-Object System.Windows.Forms.GroupBox
        $stepsGroup.Location = New-Object System.Drawing.Point(20, 280)
        $stepsGroup.Size = New-Object System.Drawing.Size(560, 150)
        $stepsGroup.Text = "Next Steps"
        $form.Controls.Add($stepsGroup)

        $stepsText = @"
1. Configure your WiFi access points:
   - RADIUS Server: [Server IP above]
   - Auth Port: 1812, Acct Port: 1813
   - Shared Secret: $($script:InstallConfig.RadiusSecret)

2. Configure client devices:
   - Security: WPA2-Enterprise
   - EAP Method: TTLS, Phase 2: PAP
   - Username: user@$($script:InstallConfig.GoogleDomain)
   - Password: [User's Google password]

3. Test authentication:
   wsl -d Ubuntu-20.04 radtest user@$($script:InstallConfig.GoogleDomain) password localhost 0 $($script:InstallConfig.RadiusSecret)

4. Manage service:
   - Use Services.msc to manage FreeRADIUS-GoogleSSO service
   - View logs in Event Viewer under Applications
"@

        $stepsTextBox = New-Object System.Windows.Forms.TextBox
        $stepsTextBox.Location = New-Object System.Drawing.Point(20, 25)
        $stepsTextBox.Size = New-Object System.Drawing.Size(520, 110)
        $stepsTextBox.Multiline = $true
        $stepsTextBox.ScrollBars = "Vertical"
        $stepsTextBox.ReadOnly = $true
        $stepsTextBox.Text = $stepsText
        $stepsGroup.Controls.Add($stepsTextBox)

    } else {
        $statusLabel = New-Object System.Windows.Forms.Label
        $statusLabel.Location = New-Object System.Drawing.Point(20, 20)
        $statusLabel.Size = New-Object System.Drawing.Size(560, 40)
        $statusLabel.Text = "Installation completed with errors. Please check the log file."
        $statusLabel.Font = New-Object System.Drawing.Font("Microsoft Sans Serif", 12, [System.Drawing.FontStyle]::Bold)
        $statusLabel.ForeColor = [System.Drawing.Color]::Red
        $form.Controls.Add($statusLabel)
    }

    # Buttons
    $okButton = New-Object System.Windows.Forms.Button
    $okButton.Location = New-Object System.Drawing.Point(500, 450)
    $okButton.Size = New-Object System.Drawing.Size(80, 30)
    $okButton.Text = "OK"
    $okButton.DialogResult = "OK"
    $form.Controls.Add($okButton)

    $form.AcceptButton = $okButton
    $form.ShowDialog() | Out-Null
}

# Main installation function
function Start-Installation {
    Write-Log "Starting FreeRADIUS Google SSO installation..."

    # Create installation directory
    New-Item -ItemType Directory -Force -Path $script:InstallConfig.InstallPath | Out-Null

    $success = $true

    # Step 1: Install prerequisites
    if (-not (Install-Prerequisites)) {
        $success = $false
    }

    # Step 2: Deploy FreeRADIUS
    if ($success -and -not (Deploy-FreeRADIUS)) {
        $success = $false
    }

    # Step 3: Install Windows service
    if ($success -and -not (Install-WindowsService)) {
        $success = $false
    }

    # Step 4: Configure firewall
    Configure-WindowsFirewall | Out-Null

    # Step 5: Create desktop shortcuts
    Create-DesktopShortcuts | Out-Null

    # Step 6: Test installation
    if ($success) {
        $success = Test-Installation
    }

    Write-Log "Installation completed with status: $(if ($success) { 'SUCCESS' } else { 'FAILED' })"

    # Show completion dialog
    Show-CompletionDialog -Success $success

    return $success
}

# Main execution logic
if ($Help) {
    Write-Host "FreeRADIUS Google SSO One-Click Installer"
    Write-Host "Usage: .\FreeRADIUS-GoogleSSO-Installer.ps1 [-Silent] [-GUI] [-Help]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Silent    Run in silent mode (minimal prompts)"
    Write-Host "  -GUI       Show GUI interface (default: true)"
    Write-Host "  -Help      Show this help message"
    Write-Host ""
    Write-Host "This installer will:"
    Write-Host "  - Install WSL2 and Ubuntu (if needed)"
    Write-Host "  - Download and compile FreeRADIUS"
    Write-Host "  - Build and install Google SSO module"
    Write-Host "  - Configure Windows service"
    Write-Host "  - Set up firewall rules"
    Write-Host "  - Create management tools"
    exit 0
}

# Check administrator privileges
if (-not (Test-Administrator)) {
    [System.Windows.Forms.MessageBox]::Show("This installer must be run as Administrator.`n`nRight-click PowerShell and select 'Run as Administrator'", "Administrator Required", "OK", "Warning")
    exit 1
}

# Show welcome dialog and collect configuration
if ($GUI -and -not $Silent) {
    $result = Show-WelcomeDialog
    if ($result -ne "OK") {
        Write-Host "Installation cancelled by user."
        exit 0
    }
} else {
    # Silent mode - use default configuration or prompt for required values
    if ([string]::IsNullOrWhiteSpace($script:InstallConfig.GoogleDomain)) {
        $script:InstallConfig.GoogleDomain = Read-Host "Enter your Google Workspace domain"
    }
    if ([string]::IsNullOrWhiteSpace($script:InstallConfig.AdminEmail)) {
        $script:InstallConfig.AdminEmail = Read-Host "Enter your admin email"
    }
    if ([string]::IsNullOrWhiteSpace($script:InstallConfig.ServiceAccountKeyPath)) {
        $script:InstallConfig.ServiceAccountKeyPath = Read-Host "Enter path to service account key file"
    }
}

# Validate required configuration
if ([string]::IsNullOrWhiteSpace($script:InstallConfig.GoogleDomain) -or
    [string]::IsNullOrWhiteSpace($script:InstallConfig.AdminEmail) -or
    [string]::IsNullOrWhiteSpace($script:InstallConfig.ServiceAccountKeyPath) -or
    -not (Test-Path $script:InstallConfig.ServiceAccountKeyPath)) {

    Write-Host "Error: Missing required configuration. Please provide:" -ForegroundColor Red
    Write-Host "  - Google Workspace domain"
    Write-Host "  - Admin email address"
    Write-Host "  - Valid service account key file path"
    exit 1
}

# Start installation
Write-Host "Starting FreeRADIUS Google SSO installation..." -ForegroundColor Green
Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Domain: $($script:InstallConfig.GoogleDomain)"
Write-Host "  Admin: $($script:InstallConfig.AdminEmail)"
Write-Host "  Key: $($script:InstallConfig.ServiceAccountKeyPath)"
Write-Host "  Install Path: $($script:InstallConfig.InstallPath)"
Write-Host ""

$installResult = Start-Installation

if ($installResult) {
    Write-Host "Installation completed successfully!" -ForegroundColor Green
    Write-Host "Check the desktop shortcuts for management tools." -ForegroundColor Green
    exit 0
} else {
    Write-Host "Installation failed. Check the log file for details:" -ForegroundColor Red
    Write-Host "  $($script:InstallConfig.InstallPath)\install.log"
    exit 1
}
