# Test Ubuntu Detection Script
# This script tests the Ubuntu detection logic used in the installer

param(
    [switch]$Verbose
)

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

Write-TestLog "Testing Ubuntu detection logic..." "SUCCESS"

# Test 1: Check WSL availability
Write-TestLog "Test 1: Checking WSL availability"
try {
    $wslVersion = wsl --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-TestLog "WSL is available" "SUCCESS"
        if ($Verbose) {
            Write-TestLog "WSL Version: $wslVersion"
        }
    } else {
        Write-TestLog "WSL is not available" "ERROR"
        exit 1
    }
} catch {
    Write-TestLog "Error checking WSL: $($_.Exception.Message)" "ERROR"
    exit 1
}

# Test 2: List distributions
Write-TestLog "Test 2: Listing WSL distributions"
try {
    $distributions = wsl --list --quiet 2>$null
    Write-TestLog "Raw distributions output: '$distributions'"
    
    if ($LASTEXITCODE -eq 0) {
        Write-TestLog "Successfully listed distributions" "SUCCESS"
        
        if ($distributions) {
            Write-TestLog "Found distributions:"
            $distributions | ForEach-Object { 
                if ($_ -and $_.Trim()) {
                    Write-TestLog "  - '$_'"
                }
            }
        } else {
            Write-TestLog "No distributions found"
        }
    } else {
        Write-TestLog "Failed to list distributions (exit code: $LASTEXITCODE)" "ERROR"
    }
} catch {
    Write-TestLog "Error listing distributions: $($_.Exception.Message)" "ERROR"
}

# Test 3: Check for Ubuntu specifically
Write-TestLog "Test 3: Checking for Ubuntu distributions"
$ubuntuFound = $false
$ubuntuDistributions = @()

if ($distributions) {
    foreach ($dist in $distributions) {
        if ($dist -and $dist.Trim() -and $dist -like "*Ubuntu*" -and $dist -notlike "*docker*") {
            $ubuntuFound = $true
            $ubuntuDistributions += $dist.Trim()
            Write-TestLog "Found Ubuntu distribution: '$($dist.Trim())'" "SUCCESS"
        }
    }
}

if (-not $ubuntuFound) {
    Write-TestLog "No Ubuntu distributions found" "WARNING"
} else {
    Write-TestLog "Ubuntu distributions found: $($ubuntuDistributions.Count)" "SUCCESS"
}

# Test 4: Test Ubuntu functionality
if ($ubuntuFound) {
    Write-TestLog "Test 4: Testing Ubuntu functionality"
    
    foreach ($ubuntuDist in $ubuntuDistributions) {
        Write-TestLog "Testing distribution: '$ubuntuDist'"
        
        try {
            # Try to run a simple command
            $testResult = wsl -d $ubuntuDist -e echo "test" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-TestLog "Distribution '$ubuntuDist' is working" "SUCCESS"
            } else {
                Write-TestLog "Distribution '$ubuntuDist' is not responding (exit code: $LASTEXITCODE)" "ERROR"
            }
        } catch {
            Write-TestLog "Error testing '$ubuntuDist': $($_.Exception.Message)" "ERROR"
        }
    }
    
    # Test common Ubuntu distribution names
    $commonNames = @("Ubuntu-20.04", "Ubuntu")
    foreach ($name in $commonNames) {
        Write-TestLog "Testing common name: '$name'"
        try {
            $testResult = wsl -d $name -e echo "test" 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-TestLog "Common name '$name' is working" "SUCCESS"
            } else {
                Write-TestLog "Common name '$name' is not working (exit code: $LASTEXITCODE)" "WARNING"
            }
        } catch {
            Write-TestLog "Error testing common name '$name': $($_.Exception.Message)" "WARNING"
        }
    }
}

# Test 5: Simulate install command
Write-TestLog "Test 5: Simulating install command"
try {
    $installResult = wsl --install -d Ubuntu-20.04 --no-launch 2>&1
    Write-TestLog "Install command output: '$installResult'"
    Write-TestLog "Install command exit code: $LASTEXITCODE"
    
    # Check for "already installed" message
    if ($installResult -like "*already installed*" -or $installResult -like "*operation completed successfully*") {
        Write-TestLog "Install command indicates Ubuntu is already installed" "SUCCESS"
    } elseif ($LASTEXITCODE -eq 0) {
        Write-TestLog "Install command succeeded (new installation)" "SUCCESS"
    } else {
        Write-TestLog "Install command failed" "ERROR"
    }
} catch {
    Write-TestLog "Error running install command: $($_.Exception.Message)" "ERROR"
}

# Summary
Write-TestLog "=== SUMMARY ===" "SUCCESS"
Write-TestLog "WSL Available: YES"
Write-TestLog "Ubuntu Found: $(if ($ubuntuFound) { 'YES' } else { 'NO' })"
Write-TestLog "Ubuntu Count: $($ubuntuDistributions.Count)"

if ($ubuntuDistributions.Count -gt 0) {
    Write-TestLog "Ubuntu Distributions:"
    $ubuntuDistributions | ForEach-Object { Write-TestLog "  - $_" }
}

Write-TestLog "Test completed."
