# Fix for "Ubuntu Already Installed" Hanging Issue

## Problem Description

The installer was getting stuck with this output:
```
Ubuntu install command result: Ubuntu 20.04 LTS is already installed. The operation completed successfully.
```

The installer would then hang indefinitely because it was waiting for an installation that was already complete.

## Root Cause

The original logic had two issues:

1. **Poor initial detection**: The initial check for existing Ubuntu installations wasn't robust enough
2. **No handling of "already installed" response**: When `wsl --install` returned "already installed", the installer still tried to wait for installation completion

## Fix Implementation

### 1. Improved Initial Ubuntu Detection

**Before** (problematic):
```powershell
$distributions = wsl --list --quiet 2>$null
if ($distributions -contains "Ubuntu-20.04" -or $distributions -contains "Ubuntu") {
    $ubuntuInstalled = $true
}
```

**After** (robust):
```powershell
$distributions = wsl --list --quiet 2>$null
Write-Log "WSL distributions found: $distributions"

$ubuntuFound = $false
foreach ($dist in $distributions) {
    if ($dist -like "*Ubuntu*" -and $dist -notlike "*docker*") {
        $ubuntuFound = $true
        Write-Log "Found Ubuntu distribution: $dist"
        break
    }
}
```

### 2. Handle "Already Installed" Response

**Before** (would hang):
```powershell
wsl --install -d Ubuntu-20.04 --no-launch
# Always wait for installation regardless of output
do {
    Start-Sleep -Seconds 5
    # Check if installation complete...
} while (...)
```

**After** (smart detection):
```powershell
$installResult = wsl --install -d Ubuntu-20.04 --no-launch 2>&1
Write-Log "Ubuntu install command result: $installResult"

# Check if Ubuntu is already installed based on the output
if ($installResult -like "*already installed*" -or $installResult -like "*operation completed successfully*") {
    Write-Log "Ubuntu is already installed, skipping installation wait"
    $installed = $true
} else {
    # Installation is in progress, wait for completion
    $installed = $false
}

# Only wait if installation is actually in progress
if (-not $installed) {
    # Wait for installation logic...
}
```

### 3. Added Ubuntu Verification

After determining Ubuntu is installed, verify it's actually working:

```powershell
# Verify Ubuntu is actually working
if ($installed) {
    $progressLabel.Text = "Verifying Ubuntu installation..."
    
    # Test if we can run a simple command in Ubuntu
    $testResult = wsl -d Ubuntu-20.04 -e echo "test" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "Ubuntu verification successful"
    } else {
        # Try default Ubuntu name
        $testResult = wsl -d Ubuntu -e echo "test" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Default Ubuntu verification successful"
        } else {
            throw "Ubuntu is installed but not responding to commands"
        }
    }
}
```

## Testing Tools

### 1. Test Ubuntu Detection Script

Run `test-ubuntu-detection.ps1` to test the detection logic:

```powershell
# Basic test
.\test-ubuntu-detection.ps1

# Verbose output
.\test-ubuntu-detection.ps1 -Verbose
```

This script will:
- Check WSL availability
- List all distributions
- Test Ubuntu detection logic
- Simulate the install command
- Verify Ubuntu functionality

### 2. Enhanced Diagnostic Tool

The updated `wsl2-diagnostic.ps1` now includes:
- Better Ubuntu detection
- Functionality testing
- Handling of "already installed" scenarios

## Expected Behavior Now

1. **If Ubuntu is not installed**: 
   - Installer runs `wsl --install`
   - Waits for installation with timeout
   - Verifies installation when complete

2. **If Ubuntu is already installed**:
   - Initial detection should catch it (improved logic)
   - If not caught initially, install command returns "already installed"
   - Installer recognizes this and skips waiting
   - Proceeds to verification step

3. **If Ubuntu is installed but not working**:
   - Verification step catches this
   - Provides appropriate error message

## Files Modified

1. **`FreeRADIUS-GoogleSSO-Installer.ps1`**:
   - Improved Ubuntu detection logic
   - Added "already installed" response handling
   - Added Ubuntu verification step

2. **`wsl2-diagnostic.ps1`**:
   - Enhanced Ubuntu detection and testing
   - Better handling of edge cases

3. **`test-ubuntu-detection.ps1`** (new):
   - Standalone test script for Ubuntu detection logic

## Testing the Fix

To test this specific scenario:

1. **Ensure Ubuntu is already installed** in WSL2
2. **Run the test script**: `.\test-ubuntu-detection.ps1 -Verbose`
3. **Run the installer** and verify it no longer hangs
4. **Check the logs** to see the improved detection messages

The installer should now properly detect existing Ubuntu installations and proceed without hanging.
