# WSL2 Diagnostic Script for FreeRADIUS Google SSO Installer
# This script helps diagnose WSL2 installation issues

param(
    [switch]$Fix,
    [switch]$Verbose
)

# Ensure running as administrator
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script must be run as Administrator"
    exit 1
}

function Write-DiagnosticLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Test-WindowsVersion {
    Write-DiagnosticLog "Checking Windows version..."
    
    $version = [System.Environment]::OSVersion.Version
    $build = (Get-ItemProperty "HKLM:SOFTWARE\Microsoft\Windows NT\CurrentVersion").CurrentBuild
    
    Write-DiagnosticLog "Windows Version: $($version.Major).$($version.Minor) Build $build"
    
    # WSL2 requires Windows 10 version 2004 (build 19041) or later
    if ([int]$build -ge 19041) {
        Write-DiagnosticLog "Windows version supports WSL2" "SUCCESS"
        return $true
    } else {
        Write-DiagnosticLog "Windows version too old for WSL2. Requires build 19041 or later." "ERROR"
        return $false
    }
}

function Test-WSLFeatures {
    Write-DiagnosticLog "Checking WSL features..."
    
    try {
        # Check WSL feature
        $wslFeature = Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux
        Write-DiagnosticLog "WSL Feature State: $($wslFeature.State)"
        
        # Check Virtual Machine Platform feature
        $vmFeature = Get-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform
        Write-DiagnosticLog "Virtual Machine Platform State: $($vmFeature.State)"
        
        $bothEnabled = ($wslFeature.State -eq "Enabled") -and ($vmFeature.State -eq "Enabled")
        
        if ($bothEnabled) {
            Write-DiagnosticLog "Required WSL features are enabled" "SUCCESS"
        } else {
            Write-DiagnosticLog "Required WSL features are not enabled" "WARNING"
            
            if ($Fix) {
                Write-DiagnosticLog "Attempting to enable WSL features..."
                
                if ($wslFeature.State -ne "Enabled") {
                    Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux -All -NoRestart
                    Write-DiagnosticLog "Enabled WSL feature"
                }
                
                if ($vmFeature.State -ne "Enabled") {
                    Enable-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform -All -NoRestart
                    Write-DiagnosticLog "Enabled Virtual Machine Platform feature"
                }
                
                Write-DiagnosticLog "Features enabled. Restart required." "WARNING"
                return $false
            }
        }
        
        return $bothEnabled
        
    } catch {
        Write-DiagnosticLog "Error checking WSL features: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-WSLCommand {
    Write-DiagnosticLog "Testing WSL command availability..."
    
    try {
        $wslPath = Get-Command wsl -ErrorAction Stop
        Write-DiagnosticLog "WSL command found at: $($wslPath.Source)" "SUCCESS"
        
        # Test WSL version
        $versionOutput = wsl --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-DiagnosticLog "WSL version output:" "SUCCESS"
            $versionOutput | ForEach-Object { Write-DiagnosticLog "  $_" }
        } else {
            Write-DiagnosticLog "WSL version command failed: $versionOutput" "WARNING"
        }
        
        # Test WSL status
        $statusOutput = wsl --status 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-DiagnosticLog "WSL status:" "SUCCESS"
            $statusOutput | ForEach-Object { Write-DiagnosticLog "  $_" }
        } else {
            Write-DiagnosticLog "WSL status command failed: $statusOutput" "WARNING"
        }
        
        return $true
        
    } catch {
        Write-DiagnosticLog "WSL command not found or not working" "ERROR"
        return $false
    }
}

function Test-WSLDistributions {
    Write-DiagnosticLog "Checking WSL distributions..."

    try {
        $distributions = wsl --list --verbose 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-DiagnosticLog "WSL distributions:" "SUCCESS"
            $distributions | ForEach-Object { Write-DiagnosticLog "  $_" }

            # Check for Ubuntu specifically
            $ubuntuFound = $false
            $ubuntuWorking = $false
            $distributions | ForEach-Object {
                if ($_ -like "*Ubuntu*" -and $_ -notlike "*docker*") {
                    $ubuntuFound = $true
                    Write-DiagnosticLog "Found Ubuntu distribution: $_"
                }
            }

            if ($ubuntuFound) {
                Write-DiagnosticLog "Ubuntu distribution found" "SUCCESS"

                # Test if Ubuntu is actually working
                Write-DiagnosticLog "Testing Ubuntu functionality..."
                try {
                    $testResult = wsl -d Ubuntu-20.04 -e echo "test" 2>$null
                    if ($LASTEXITCODE -eq 0) {
                        Write-DiagnosticLog "Ubuntu-20.04 is working" "SUCCESS"
                        $ubuntuWorking = $true
                    } else {
                        Write-DiagnosticLog "Ubuntu-20.04 not responding, trying default Ubuntu..." "WARNING"
                        $testResult = wsl -d Ubuntu -e echo "test" 2>$null
                        if ($LASTEXITCODE -eq 0) {
                            Write-DiagnosticLog "Default Ubuntu is working" "SUCCESS"
                            $ubuntuWorking = $true
                        } else {
                            Write-DiagnosticLog "Ubuntu is installed but not responding" "ERROR"
                        }
                    }
                } catch {
                    Write-DiagnosticLog "Error testing Ubuntu: $($_.Exception.Message)" "ERROR"
                }

            } else {
                Write-DiagnosticLog "Ubuntu distribution not found" "WARNING"

                if ($Fix) {
                    Write-DiagnosticLog "Attempting to install Ubuntu..."
                    $installOutput = wsl --install -d Ubuntu-20.04 --no-launch 2>&1
                    Write-DiagnosticLog "Install output: $installOutput"

                    if ($installOutput -like "*already installed*") {
                        Write-DiagnosticLog "Ubuntu was already installed" "SUCCESS"
                        $ubuntuFound = $true
                    } elseif ($LASTEXITCODE -eq 0) {
                        Write-DiagnosticLog "Ubuntu installation started" "SUCCESS"
                    } else {
                        Write-DiagnosticLog "Ubuntu installation failed" "ERROR"
                    }
                }
            }

            return $ubuntuFound -and $ubuntuWorking

        } else {
            Write-DiagnosticLog "Failed to list WSL distributions: $distributions" "ERROR"
            return $false
        }

    } catch {
        Write-DiagnosticLog "Error checking WSL distributions: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-NetworkConnectivity {
    Write-DiagnosticLog "Testing network connectivity..."
    
    $testUrls = @(
        "https://aka.ms/wslubuntu2004",
        "https://github.com",
        "https://packages.microsoft.com"
    )
    
    $allSuccess = $true
    
    foreach ($url in $testUrls) {
        try {
            $response = Invoke-WebRequest -Uri $url -Method Head -TimeoutSec 10 -UseBasicParsing
            Write-DiagnosticLog "Network test to ${url}: SUCCESS (Status: $($response.StatusCode))" "SUCCESS"
        } catch {
            Write-DiagnosticLog "Network test to ${url}: FAILED ($($_.Exception.Message))" "ERROR"
            $allSuccess = $false
        }
    }
    
    return $allSuccess
}

function Test-HyperV {
    Write-DiagnosticLog "Checking Hyper-V compatibility..."
    
    try {
        $hyperVInfo = Get-ComputerInfo -Property HyperV*
        
        if ($hyperVInfo.HyperVRequirementDataExecutionPreventionAvailable -and 
            $hyperVInfo.HyperVRequirementSecondLevelAddressTranslation -and
            $hyperVInfo.HyperVRequirementVirtualizationFirmwareEnabled -and
            $hyperVInfo.HyperVRequirementVMMonitorModeExtensions) {
            Write-DiagnosticLog "Hyper-V requirements met" "SUCCESS"
            return $true
        } else {
            Write-DiagnosticLog "Hyper-V requirements not met" "ERROR"
            Write-DiagnosticLog "DEP Available: $($hyperVInfo.HyperVRequirementDataExecutionPreventionAvailable)"
            Write-DiagnosticLog "SLAT Available: $($hyperVInfo.HyperVRequirementSecondLevelAddressTranslation)"
            Write-DiagnosticLog "Virtualization Enabled: $($hyperVInfo.HyperVRequirementVirtualizationFirmwareEnabled)"
            Write-DiagnosticLog "VM Extensions: $($hyperVInfo.HyperVRequirementVMMonitorModeExtensions)"
            return $false
        }
    } catch {
        Write-DiagnosticLog "Could not check Hyper-V requirements: $($_.Exception.Message)" "WARNING"
        return $true  # Assume OK if we can't check
    }
}

# Main diagnostic routine
Write-DiagnosticLog "Starting WSL2 diagnostic..." "SUCCESS"
Write-DiagnosticLog "Fix mode: $Fix"

$results = @{
    WindowsVersion = Test-WindowsVersion
    WSLFeatures = Test-WSLFeatures
    WSLCommand = Test-WSLCommand
    WSLDistributions = Test-WSLDistributions
    NetworkConnectivity = Test-NetworkConnectivity
    HyperVCompatibility = Test-HyperV
}

Write-DiagnosticLog "`n=== DIAGNOSTIC SUMMARY ===" "SUCCESS"

$allPassed = $true
foreach ($test in $results.Keys) {
    $status = if ($results[$test]) { "PASS" } else { "FAIL" }
    $color = if ($results[$test]) { "Green" } else { "Red" }
    Write-Host "$test`: $status" -ForegroundColor $color
    
    if (-not $results[$test]) {
        $allPassed = $false
    }
}

if ($allPassed) {
    Write-DiagnosticLog "`nAll tests passed! WSL2 should be working correctly." "SUCCESS"
} else {
    Write-DiagnosticLog "`nSome tests failed. Please address the issues above." "ERROR"
    
    if (-not $Fix) {
        Write-DiagnosticLog "Run with -Fix parameter to attempt automatic fixes." "INFO"
    }
}

Write-DiagnosticLog "`nDiagnostic complete."
