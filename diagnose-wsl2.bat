@echo off
REM WSL2 Diagnostic Launcher for FreeRADIUS Google SSO Installer
REM This batch file launches the WSL2 diagnostic script with proper permissions

echo FreeRADIUS Google SSO - WSL2 Diagnostic Tool
echo =============================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator: YES
    echo.
    goto :run_diagnostic
) else (
    echo Running as Administrator: NO
    echo.
    echo This diagnostic tool requires Administrator privileges.
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

:run_diagnostic
echo Starting WSL2 diagnostic...
echo.

REM Run the PowerShell diagnostic script
powershell.exe -ExecutionPolicy Bypass -File "%~dp0wsl2-diagnostic.ps1" -Verbose

echo.
echo Diagnostic complete. 
echo.
echo If you found issues, you can run the diagnostic with automatic fixes:
echo   powershell.exe -ExecutionPolicy Bypass -File "%~dp0wsl2-diagnostic.ps1" -Fix -Verbose
echo.
pause
